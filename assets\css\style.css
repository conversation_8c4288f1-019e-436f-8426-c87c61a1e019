/* Global Styles */
:root {
    --primary-color: #007bff;
    --primary-hover: #0069d9;
    --secondary-color: #6c757d;
    --light-bg: #f8f9fa;
    --border-color: rgba(0, 0, 0, 0.125);
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --chat-sender: #e9ecef;
    --chat-receiver: #d4edff;
    --shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --transition: all 0.3s ease;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--light-bg);
    font-size: 16px;
    line-height: 1.5;
    color: #333;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    margin-bottom: 1rem;
}

/* Mobile-first approach */
.container-fluid {
    padding-left: 15px;
    padding-right: 15px;
}

/* Navbar Styles */
.navbar {
    padding: 0.5rem 1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.25rem;
}

.navbar-toggler {
    border: none;
    padding: 0.5rem;
}

.navbar-toggler:focus {
    outline: none;
    box-shadow: none;
}

/* Sidebar Styles */
.sidebar {
    position: fixed;
    top: 56px;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 48px 0 0;
    box-shadow: inset -1px 0 0 var(--border-color);
    height: calc(100vh - 56px);
    overflow-y: auto;
    transition: var(--transition);
}

.sidebar .nav-link {
    font-weight: 500;
    color: #333;
    padding: 0.75rem 1rem;
    border-left: 3px solid transparent;
    display: flex;
    align-items: center;
    transition: var(--transition);
}

.sidebar .nav-link i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

.sidebar .nav-link:hover {
    color: var(--primary-color);
    background-color: #f0f0f0;
    border-left: 3px solid var(--primary-color);
}

.sidebar .nav-link.active {
    color: var(--primary-color);
    background-color: #e9ecef;
    border-left: 3px solid var(--primary-color);
}

.sidebar .nav-link .badge {
    margin-left: auto;
}

/* Mobile Sidebar Toggle */
.sidebar-toggle {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    border: none;
    outline: none;
    display: none;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    transition: transform 0.3s, background-color 0.3s;
}

.sidebar-toggle:hover, .sidebar-toggle:focus {
    background-color: var(--primary-hover);
    transform: scale(1.05);
    color: white;
}

.sidebar-toggle:active {
    transform: scale(0.95);
}

/* Main Content */
main {
    padding-top: 20px;
    transition: var(--transition);
}

/* Card Styles */
.card {
    margin-bottom: 20px;
    border: none;
    border-radius: 0.5rem;
    box-shadow: var(--shadow);
    overflow: hidden;
}

.card-header {
    font-weight: 600;
    background-color: var(--light-bg);
    border-bottom: 1px solid var(--border-color);
    padding: 0.75rem 1.25rem;
}

.card-body {
    padding: 1.25rem;
}

.quick-access-card {
    text-align: center;
    transition: transform 0.3s;
    height: 100%;
    border: none;
    border-radius: 0.5rem;
    box-shadow: var(--shadow);
    overflow: hidden;
}

.quick-access-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.quick-access-card .card-body {
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    height: 100%;
    min-height: 280px;
}

.quick-access-card .card-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    text-align: center;
}

.quick-access-card i {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.quick-access-card .card-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: #333;
}

.quick-access-card .card-text {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 0;
    line-height: 1.4;
}

.quick-access-card .card-action {
    margin-top: 1rem;
}

.quick-access-card .btn {
    font-size: 0.9rem;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    transition: all 0.3s ease;
}

/* Dashboard Cards Row Responsive Layout */
.dashboard-cards-row {
    margin-left: -0.5rem;
    margin-right: -0.5rem;
}

.dashboard-cards-row > [class*="col-"] {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
}

/* Responsive adjustments for 5-card layout */
@media (min-width: 1200px) {
    .dashboard-cards-row {
        justify-content: center;
    }

    .dashboard-cards-row .col-xl-2 {
        flex: 0 0 18%;
        max-width: 18%;
    }
}

@media (max-width: 1199.98px) and (min-width: 992px) {
    .dashboard-cards-row .col-lg-3:nth-child(5) {
        margin-left: auto;
        margin-right: auto;
        flex: 0 0 25%;
        max-width: 25%;
    }
}

@media (max-width: 991.98px) and (min-width: 768px) {
    .dashboard-cards-row .col-md-4:nth-child(4),
    .dashboard-cards-row .col-md-4:nth-child(5) {
        margin-left: auto;
        margin-right: auto;
        flex: 0 0 33.333333%;
        max-width: 33.333333%;
    }
}

@media (max-width: 767.98px) {
    .quick-access-card .card-body {
        min-height: 250px;
        padding: 1.25rem;
    }

    .quick-access-card i {
        font-size: 2rem;
    }

    .quick-access-card .card-title {
        font-size: 1rem;
    }

    .quick-access-card .card-text {
        font-size: 0.85rem;
    }
}

/* Table Styles */
.table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

.table {
    width: 100%;
    margin-bottom: 1rem;
    color: #212529;
}

.table th {
    font-weight: 600;
    background-color: var(--light-bg);
    white-space: nowrap;
}

.table td {
    vertical-align: middle;
}

/* Mobile-friendly tables */
@media (max-width: 767.98px) {
    .table-mobile-responsive {
        display: block;
        width: 100%;
    }

    .table-mobile-responsive thead {
        display: none;
    }

    .table-mobile-responsive tbody,
    .table-mobile-responsive tr {
        display: block;
        width: 100%;
    }

    .table-mobile-responsive td {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem;
        border-bottom: 1px solid #dee2e6;
    }

    .table-mobile-responsive td:before {
        content: attr(data-label);
        font-weight: 600;
        margin-right: 1rem;
    }

    .table-mobile-responsive tr {
        margin-bottom: 1rem;
        border: 1px solid #dee2e6;
        border-radius: 0.25rem;
    }
}

/* Form Styles */
.form-control {
    height: calc(1.5em + 0.75rem + 2px);
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
}

.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-group {
    margin-bottom: 1rem;
}

.custom-file-label {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Button Styles */
.btn {
    font-weight: 500;
    padding: 0.375rem 0.75rem;
    border-radius: 0.25rem;
    transition: var(--transition);
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--primary-hover);
    border-color: var(--primary-hover);
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

/* Status Badges */
.badge {
    font-weight: 500;
    padding: 0.35em 0.65em;
    border-radius: 0.25rem;
}

.badge-pending {
    background-color: var(--warning-color);
    color: #212529;
}

.badge-matched {
    background-color: var(--info-color);
    color: #fff;
}

.badge-completed {
    background-color: var(--success-color);
    color: #fff;
}

.badge-cancelled {
    background-color: var(--danger-color);
    color: #fff;
}

/* Chat Styles */
.chat-container {
    height: 400px;
    overflow-y: auto;
    padding: 1rem;
    background-color: #fff;
    border-radius: 0.25rem;
    border: 1px solid #dee2e6;
    display: flex;
    flex-direction: column;
}

.chat-message {
    margin-bottom: 15px;
    padding: 10px;
    border-radius: 10px;
    max-width: 80%;
    word-wrap: break-word;
    position: relative;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.chat-message-sender {
    background-color: var(--chat-sender);
    margin-right: auto;
    border-bottom-left-radius: 0;
}

.chat-message-sender:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: -10px;
    width: 0;
    height: 0;
    border: 10px solid transparent;
    border-right-color: var(--chat-sender);
    border-left: 0;
    border-bottom: 0;
    margin-left: 0;
    margin-bottom: 0;
}

.chat-message-receiver {
    background-color: var(--chat-receiver);
    margin-left: auto;
    border-bottom-right-radius: 0;
}

.chat-message-receiver:after {
    content: '';
    position: absolute;
    bottom: 0;
    right: -10px;
    width: 0;
    height: 0;
    border: 10px solid transparent;
    border-left-color: var(--chat-receiver);
    border-right: 0;
    border-bottom: 0;
    margin-right: 0;
    margin-bottom: 0;
}

.chat-message-time {
    font-size: 0.75rem;
    color: var(--secondary-color);
}

/* Message Form */
.message-form {
    margin-top: 1rem;
    border-top: 1px solid #dee2e6;
    padding-top: 1rem;
}

.auto-resize {
    min-height: 38px;
    max-height: 150px;
    resize: none;
    overflow-y: hidden;
}

/* Notification Styles */
.notification-item {
    padding: 15px;
    border-bottom: 1px solid #e9ecef;
    transition: background-color 0.3s;
}

.notification-item:hover {
    background-color: var(--light-bg);
}

.notification-item.unread {
    background-color: #f0f7ff;
}

.notification-time {
    font-size: 0.75rem;
    color: var(--secondary-color);
}

/* Responsive Adjustments */
@media (max-width: 991.98px) {
    .sidebar {
        width: 250px;
        transform: translateX(-100%);
        z-index: 1050;
        box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
    }

    .sidebar.show {
        transform: translateX(0);
    }

    main {
        margin-left: 0 !important;
        width: 100%;
    }

    .sidebar-toggle {
        display: flex !important;
        position: fixed;
        bottom: 20px;
        right: 20px;
        z-index: 1060;
    }

    .chat-container {
        height: 300px;
    }

    .chat-message {
        max-width: 90%;
    }

    /* Add overlay when sidebar is shown */
    body.sidebar-open::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 1040;
    }
}

@media (max-width: 767.98px) {
    body {
        font-size: 14px;
    }

    .container-fluid {
        padding-left: 10px;
        padding-right: 10px;
    }

    .card-header {
        padding: 0.5rem 1rem;
    }

    .card-body {
        padding: 1rem;
    }

    .btn {
        padding: 0.25rem 0.5rem;
    }

    .chat-container {
        height: 250px;
    }

    .chat-message {
        padding: 8px;
        margin-bottom: 10px;
    }

    .quick-access-card i {
        font-size: 1.5rem;
    }

    h1.h2 {
        font-size: 1.5rem;
    }

    .form-control {
        font-size: 14px;
    }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Tutorials Styles */
.tutorial-card {
    transition: all 0.3s ease;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
}

.tutorial-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    border-color: #28a745;
}

.tutorial-card .card-img-top {
    height: 200px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.tutorial-card:hover .card-img-top {
    transform: scale(1.05);
}

.tutorial-content {
    line-height: 1.7;
    font-size: 14px;
}

.tutorial-content h1,
.tutorial-content h2,
.tutorial-content h3,
.tutorial-content h4,
.tutorial-content h5,
.tutorial-content h6 {
    color: #28a745;
    margin-top: 1.5rem;
    margin-bottom: 1rem;
}

.tutorial-content p {
    margin-bottom: 1rem;
}

.tutorial-content ul,
.tutorial-content ol {
    margin-bottom: 1rem;
    padding-left: 1.5rem;
}

.tutorial-content li {
    margin-bottom: 0.5rem;
}

.tutorial-content a {
    color: #28a745;
    text-decoration: none;
    font-weight: 500;
}

.tutorial-content a:hover {
    text-decoration: underline;
    color: #1e7e34;
}

.tutorial-content blockquote {
    border-left: 4px solid #28a745;
    padding-left: 1rem;
    margin: 1rem 0;
    font-style: italic;
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 4px;
}

.tutorial-content code {
    background-color: #f8f9fa;
    padding: 0.2rem 0.4rem;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
    color: #e83e8c;
}

.tutorial-content pre {
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 4px;
    overflow-x: auto;
    margin: 1rem 0;
}

.tutorial-category-filter {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border-radius: 8px;
    padding: 1rem;
    color: white;
}

.tutorial-category-filter .form-control {
    border: none;
    border-radius: 6px;
}

.tutorial-category-filter .btn {
    border-radius: 6px;
    font-weight: 500;
}

.tutorial-modal .modal-header {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border-bottom: none;
}

.tutorial-modal .modal-title {
    font-weight: 600;
}

.tutorial-modal .modal-body {
    padding: 2rem;
}

.tutorial-empty-state {
    text-align: center;
    padding: 3rem 1rem;
}

.tutorial-empty-state i {
    font-size: 4rem;
    color: #6c757d;
    margin-bottom: 1rem;
}

.tutorial-empty-state h4 {
    color: #6c757d;
    margin-bottom: 1rem;
}

.tutorial-empty-state p {
    color: #6c757d;
    font-size: 1.1rem;
}

/* Admin Tutorial Styles */
.admin-tutorial-table .table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
    color: #495057;
}

.admin-tutorial-table .table td {
    vertical-align: middle;
}

.tutorial-status-badge {
    font-size: 0.8rem;
    padding: 0.3rem 0.6rem;
}

.tutorial-media-badges .badge {
    margin-right: 0.25rem;
    font-size: 0.7rem;
}

.tutorial-order-input {
    width: 80px;
    text-align: center;
}

.tutorial-guidelines {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
}

.tutorial-guidelines ul {
    margin-bottom: 0;
}

.tutorial-guidelines li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #e9ecef;
}

.tutorial-guidelines li:last-child {
    border-bottom: none;
}

.tutorial-guidelines i {
    margin-right: 0.5rem;
    width: 16px;
}

/* Responsive Design for Tutorials */
@media (max-width: 768px) {
    .tutorial-card .card-img-top {
        height: 150px;
    }

    .tutorial-modal .modal-body {
        padding: 1rem;
    }

    .tutorial-content {
        font-size: 13px;
    }

    .tutorial-category-filter {
        margin-bottom: 1rem;
    }

    .admin-tutorial-table .table-responsive {
        font-size: 0.9rem;
    }

    .tutorial-order-input {
        width: 60px;
    }
}

@media (max-width: 576px) {
    .tutorial-card {
        margin-bottom: 1rem;
    }

    .tutorial-modal .modal-dialog {
        margin: 0.5rem;
    }

    .tutorial-empty-state {
        padding: 2rem 0.5rem;
    }

    .tutorial-empty-state i {
        font-size: 3rem;
    }
}

/* Dark Mode Compatibility - Remove conflicting styles */
@media (prefers-color-scheme: dark) {
    .tutorial-card {
        background-color: inherit;
        border-color: inherit;
        color: inherit;
    }

    .tutorial-content {
        color: inherit;
    }

    .tutorial-content h1,
    .tutorial-content h2,
    .tutorial-content h3,
    .tutorial-content h4,
    .tutorial-content h5,
    .tutorial-content h6 {
        color: inherit;
    }

    .tutorial-content blockquote {
        background-color: inherit;
        border-left-color: inherit;
    }

    .tutorial-content code {
        background-color: inherit;
        color: inherit;
    }

    .tutorial-content pre {
        background-color: inherit;
    }

    .tutorial-guidelines {
        background-color: inherit;
        color: inherit;
    }
}
